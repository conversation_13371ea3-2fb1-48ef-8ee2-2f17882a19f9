<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>ini</string>
		<string>conf</string>
	</array>
	<key>keyEquivalent</key>
	<string>^~I</string>
	<key>name</key>
	<string>Ini</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>begin</key>
			<string>(^[ \t]+)?(?=#)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.whitespace.comment.leading.ini</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(?!\G)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>#</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.ini</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\n</string>
					<key>name</key>
					<string>comment.line.number-sign.ini</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(^[ \t]+)?(?=;)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.whitespace.comment.leading.ini</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(?!\G)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>;</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.ini</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\n</string>
					<key>name</key>
					<string>comment.line.semicolon.ini</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.other.definition.ini</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.separator.key-value.ini</string>
				</dict>
			</dict>
			<key>match</key>
			<string>\b([a-zA-Z0-9_.-]+)\b\s*(=)</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.entity.ini</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.entity.ini</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^(\[)(.*?)(\])</string>
			<key>name</key>
			<string>entity.name.section.group-title.ini</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>'</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.ini</string>
				</dict>
			</dict>
			<key>end</key>
			<string>'</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.ini</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.single.ini</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\.</string>
					<key>name</key>
					<string>constant.character.escape.ini</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>"</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.ini</string>
				</dict>
			</dict>
			<key>end</key>
			<string>"</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.ini</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.ini</string>
		</dict>
	</array>
	<key>scopeName</key>
	<string>source.properties</string>
	<key>uuid</key>
	<string>77DC23B6-8A90-11D9-BAA4-000A9584EC8C</string>
</dict>
</plist>