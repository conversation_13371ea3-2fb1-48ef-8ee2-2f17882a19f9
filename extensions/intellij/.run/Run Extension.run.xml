<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Run Extension" type="GradleRunConfiguration" factoryName="Gradle">
    <ExternalSystemSettings>
      <option name="env">
        <map>
          <entry key="AIMI_URL" value="https://pre-aimi.alibaba-inc.com/#/ide/dashboard" />
          <entry key="API_URL" value="https://pre-aimi.alibaba-inc.com/#/ide/api" />
          <entry key="GUI_URL" value="http://localhost:5173/jetbrains_index.html" />
          <entry key="HISTORY_URL" value="https://pre-aimi.alibaba-inc.com/#/ide/history" />
            <entry key="ORG_GRADLE_PROJECT_platformVersion" value="2024.3"/>
          <entry key="USE_TCP" value="true" />
        </map>
      </option>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$/extensions/intellij" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value="--stacktrace" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list>
          <option value="runIde" />
        </list>
      </option>
      <option name="vmOptions" value="-Dide.browser.jcef.out-of-process.enabled=false"/>
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>false</DebugAllEnabled>
    <RunAsTest>false</RunAsTest>
    <method v="2" />
  </configuration>
</component>