[versions]
# libraries
annotations = "24.0.1"

# plugins
kotlin = "2.1.21"
changelog = "2.2.1"
gradleIntelliJPlugin = "1.17.4"
qodana = "2024.1.9"
kover = "0.8.3"
intelliJPlatform = "2.5.0"
okhttp = "4.12.0"
kotlinStdlib = "1.6.0"
junit = "4.13.2"

# 新增依赖版本
posthog = "1.+"
remoteRobot = "0.11.23"
jupiterApi = "5.10.0"
jupiterEngine = "5.9.2"
loggingInterceptor = "4.12.0"
videoRecorder = "2.0"
log4j = "2.20.0"
log4jOverSlf4j = "1.7.36"
kotlinSerialization = "1.8.1"

[libraries]
annotations = { group = "org.jetbrains", name = "annotations", version.ref = "annotations" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
kotlin-stdlib = { group = "org.jetbrains.kotlin", name = "kotlin-stdlib", version.ref = "kotlinStdlib" }
junit = { group = "junit", name = "junit", version.ref = "junit" }

# 新增库定义
posthog = { group = "com.posthog.java", name = "posthog", version.ref = "posthog" }
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinSerialization" }
remote-robot = { group = "com.intellij.remoterobot", name = "remote-robot", version.ref = "remoteRobot" }
remote-fixtures = { group = "com.intellij.remoterobot", name = "remote-fixtures", version.ref = "remoteRobot" }
jupiter-api = { group = "org.junit.jupiter", name = "junit-jupiter-api", version.ref = "jupiterApi" }
jupiter-engine = { group = "org.junit.jupiter", name = "junit-jupiter-engine", version.ref = "jupiterEngine" }
logging-interceptor = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "loggingInterceptor" }
video-recorder = { group = "com.automation-remarks", name = "video-recorder-junit5", version.ref = "videoRecorder" }
log4j-core = { group = "org.apache.logging.log4j", name = "log4j-core", version.ref = "log4j" }
log4j-api = { group = "org.apache.logging.log4j", name = "log4j-api", version.ref = "log4j" }
log4j-over-slf4j = { group = "org.slf4j", name = "log4j-over-slf4j", version.ref = "log4jOverSlf4j" }

[plugins]
changelog = { id = "org.jetbrains.changelog", version.ref = "changelog" }
intelliJPlatform = { id = "org.jetbrains.intellij.platform", version.ref = "intelliJPlatform" }
gradleIntelliJPlugin = { id = "org.jetbrains.intellij", version.ref = "gradleIntelliJPlugin" }
kotlin = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kover = { id = "org.jetbrains.kotlinx.kover", version.ref = "kover" }
qodana = { id = "org.jetbrains.qodana", version.ref = "qodana" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
