package com.taobao.mc.aimi.ext.unit

import com.intellij.openapi.editor.Editor
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.ApplyState
import com.taobao.mc.aimi.ext.ApplyStateStatus
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.core.ApplyToFileHandler
import com.taobao.mc.aimi.ext.core.CoreMessenger
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.editor.EditorUtils
import com.taobao.mc.aimi.ext.protocol.ApplyToFileParams
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import junit.framework.TestCase
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest

@ExperimentalCoroutinesApi
class ApplyToFileHandlerTest : TestCase() {
    // Mock all dependencies
    private val mockProject = mockk<Project>(relaxed = true)
    private val mockAIMIPluginService = mockk<AIMIPluginService>(relaxed = true)
    private val mockIde = mockk<IDE>(relaxed = true)
    private val mockEditorUtils = mockk<EditorUtils>(relaxed = true)
    private val mockDiffStreamService = mockk<DiffStreamService>(relaxed = true)
    private val mockEditor = mockk<Editor>(relaxed = true)
    private val mockCoreMessenger = mockk<CoreMessenger>(relaxed = true)

    // Test subject
    private lateinit var handler: ApplyToFileHandler

    // Constants for testing
    private val testParams = ApplyToFileParams(
        "Sample text to apply",
        "stream123",
        "test/file.kt",
        "tool-call-123"
    )

    override fun setUp() {
        // Common setup
        every { mockEditorUtils.editor } returns mockEditor
        every { mockAIMIPluginService.coreMessenger } returns mockCoreMessenger

        // Create the handler with mocked dependencies
        handler = ApplyToFileHandler(
            mockProject,
            mockAIMIPluginService,
            mockIde,
            testParams,
            mockEditorUtils,
            mockDiffStreamService
        )
    }

    fun `test should insert text directly when document is empty`() = runTest {
        // Given
        every { mockEditorUtils.isDocumentEmpty() } returns true

        // When
        handler.handleApplyToFile()

        // Then
        verify { mockEditorUtils.insertTextIntoEmptyDocument(testParams.text) }
        verify(exactly = 0) { mockDiffStreamService.register(any(), any()) } // Ensure no diff streaming happened

        // Verify notifications sent
        verify {
            mockAIMIPluginService.sendToWebview(
                eq("updateApplyState"),
                withArg { payload ->
                    assert(payload is ApplyState)
                    assert((payload as ApplyState).status == ApplyStateStatus.STREAMING.status)
                },
                any()
            )
        }

        verify {
            mockAIMIPluginService.sendToWebview(
                eq("updateApplyState"),
                withArg { payload ->
                    assert(payload is ApplyState)
                    assert((payload as ApplyState).status == ApplyStateStatus.CLOSED.status)
                },
                any()
            )
        }
    }
}
