package com.taobao.mc.aimi.psi

import com.intellij.psi.*
import com.intellij.psi.util.PsiTreeUtil
import com.intellij.psi.util.PsiTypesUtil
import com.taobao.mc.aimi.ext.utils.toUriOrNull
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.psi.types.*

/**
 * Java 对象类型解析器
 * 专门处理 Java 语言的类型解析和 Lambda 表达式
 */
class JavaObjectTypeResolver : IObjectResolver {
    private val logger = LoggerManager.getLogger(javaClass)

    /**
     * 解析 Java 对象
     */
    override fun resolveObjects(element: PsiElement, offset: Int): List<ObjectInfo> {
        val objects = mutableListOf<ObjectInfo>()

        // 首先检查是否在方法调用的括号内
        val methodCallReceiver = findMethodCallReceiver(element)
        if (methodCallReceiver != null) {
            logger.debug("Found Java method call receiver: ${methodCallReceiver.text}")
            val receiverInfo = resolveJavaExpressionType(methodCallReceiver as PsiExpression)
            if (receiverInfo != null) {
                objects.add(receiverInfo.copy(confidence = 1.0)) // 最高优先级
            }
        }

        // 检查是否在点号后面（如：obj.）
        val dotContext = findDotContext(element)
        if (dotContext != null) {
            val objectInfo = resolveJavaExpressionType(dotContext)
            if (objectInfo != null) {
                objects.add(objectInfo)
            }
        } else {
            // 没有点号，查找当前作用域中可用的对象
            objects.addAll(findAvailableJavaObjects(element))
        }

        return objects.sortedByDescending { it.confidence }
    }

    /**
     * 解析 Java Lambda 参数
     */
    override fun resolveLambdaParameters(lambdaExpression: PsiElement): List<ObjectInfo> {
        val objects = mutableListOf<ObjectInfo>()
        if (lambdaExpression !is PsiLambdaExpression) return objects

        // 获取 Lambda 参数
        val parameterList = lambdaExpression.parameterList
        parameterList.parameters.forEach { param ->
            val paramType = param.type
            objects.add(
                ObjectInfo(
                    name = param.name,
                    type = paramType.presentableText,
                    qualifiedType = paramType.canonicalText,
                    kind = ObjectKind.LAMBDA_PARAMETER,
                    element = param,
                    members = extractJavaTypeMembers(paramType),
                    confidence = 1.0,
                    isLambdaParameter = true,
                    lambdaContext = "Java Lambda",
                    filepath = getTypeFilepath(paramType)
                )
            )
        }

        // 如果是单参数且没有显式声明，可能有隐式参数
        if (objects.isEmpty()) {
            // 尝试从上下文推断参数类型
            val contextType = inferLambdaParameterType(lambdaExpression)
            if (contextType != null) {
                objects.add(
                    ObjectInfo(
                        name = "param",
                        type = contextType.presentableText,
                        qualifiedType = contextType.canonicalText,
                        kind = ObjectKind.LAMBDA_PARAMETER,
                        element = lambdaExpression,
                        members = extractJavaTypeMembers(contextType),
                        confidence = 0.8,
                        isLambdaParameter = true,
                        lambdaContext = "Java Lambda (inferred)",
                        filepath = getTypeFilepath(contextType)
                    )
                )
            }
        }

        return objects
    }

    /**
     * 查找方法调用的接收者对象
     */
    private fun findMethodCallReceiver(element: PsiElement): PsiElement? {
        var current: PsiElement? = element

        // 向上查找，寻找方法调用表达式
        while (current != null) {
            when (current) {
                // Java方法调用
                is PsiMethodCallExpression -> {
                    return current.methodExpression.qualifierExpression
                }
            }
            current = current.parent
        }

        return null
    }

    /**
     * 查找点号上下文
     */
    private fun findDotContext(element: PsiElement): PsiExpression? {
        val parent = element.parent
        if (parent is PsiReferenceExpression && parent.qualifierExpression != null) {
            return parent.qualifierExpression
        }
        return null
    }

    /**
     * 解析Java表达式类型
     */
    private fun resolveJavaExpressionType(expression: PsiExpression): ObjectInfo? {
        val type = expression.type ?: return null

        return when (expression) {
            is PsiThisExpression -> {
                val containingClass = PsiTreeUtil.getParentOfType(expression, PsiClass::class.java)
                ObjectInfo(
                    name = "this",
                    type = containingClass?.name ?: "Unknown",
                    qualifiedType = containingClass?.qualifiedName,
                    kind = ObjectKind.THIS,
                    element = containingClass,
                    members = extractJavaClassMembers(containingClass),
                    confidence = 1.0,
                    filepath = containingClass?.containingFile?.virtualFile?.toUriOrNull()
                )
            }

            is PsiSuperExpression -> {
                val containingClass = PsiTreeUtil.getParentOfType(expression, PsiClass::class.java)
                val superClass = containingClass?.superClass
                ObjectInfo(
                    name = "super",
                    type = superClass?.name ?: "Object",
                    qualifiedType = superClass?.qualifiedName ?: "java.lang.Object",
                    kind = ObjectKind.SUPER,
                    element = superClass,
                    members = extractJavaClassMembers(superClass),
                    confidence = 1.0,
                    filepath = superClass?.containingFile?.virtualFile?.toUriOrNull()
                )
            }

            is PsiReferenceExpression -> {
                when (val resolved = expression.resolve()) {
                    is PsiVariable -> {
                        val kind = when (resolved) {
                            is PsiLocalVariable -> ObjectKind.LOCAL_VARIABLE
                            is PsiParameter -> ObjectKind.PARAMETER
                            is PsiField -> if (resolved.hasModifierProperty(PsiModifier.STATIC)) ObjectKind.STATIC_FIELD else ObjectKind.FIELD
                            else -> ObjectKind.LOCAL_VARIABLE
                        }


                        ObjectInfo(
                            name = resolved.name ?: "unknown",
                            type = type.presentableText,
                            qualifiedType = type.canonicalText,
                            kind = kind,
                            element = resolved,
                            members = extractJavaTypeMembers(type),
                            confidence = 0.9,
                            filepath = getTypeFilepath(type)
                        )
                    }

                    else -> null
                }
            }

            is PsiMethodCallExpression -> {
                val returnType = expression.type
                if (returnType != null) {
                    ObjectInfo(
                        name = "methodResult",
                        type = returnType.presentableText,
                        qualifiedType = returnType.canonicalText,
                        kind = ObjectKind.METHOD_CALL,
                        element = expression,
                        members = extractJavaTypeMembers(returnType),
                        confidence = 0.8,
                        filepath = getTypeFilepath(returnType)
                    )
                } else null
            }

            else -> {
                ObjectInfo(
                    name = "expression",
                    type = type.presentableText,
                    qualifiedType = type.canonicalText,
                    kind = ObjectKind.EXPRESSION,
                    element = expression,
                    members = extractJavaTypeMembers(type),
                    confidence = 0.7,
                    filepath = getTypeFilepath(type)
                )
            }
        }
    }

    /**
     * 查找可用的Java对象
     */
    private fun findAvailableJavaObjects(element: PsiElement): List<ObjectInfo> {
        val objects = mutableListOf<ObjectInfo>()

        // 添加this对象
        val containingClass = PsiTreeUtil.getParentOfType(element, PsiClass::class.java)
        val containingMethod = PsiTreeUtil.getParentOfType(element, PsiMethod::class.java)

        if (containingClass != null && containingMethod != null && !containingMethod.hasModifierProperty(PsiModifier.STATIC)) {
            objects.add(
                ObjectInfo(
                    name = "this",
                    type = containingClass.name ?: "Unknown",
                    qualifiedType = containingClass.qualifiedName,
                    kind = ObjectKind.THIS,
                    element = containingClass,
                    members = extractJavaClassMembers(containingClass),
                    confidence = 1.0,
                    filepath = containingClass.containingFile?.virtualFile?.toUriOrNull()
                )
            )
        }

        // 添加局部变量和参数
        var current: PsiElement? = element
        while (current != null) {
            when (current) {
                is PsiMethod -> {
                    current.parameterList.parameters.forEach { param ->
                        objects.add(
                            ObjectInfo(
                                name = param.name,
                                type = param.type.presentableText,
                                qualifiedType = param.type.canonicalText,
                                kind = ObjectKind.PARAMETER,
                                element = param,
                                members = extractJavaTypeMembers(param.type),
                                confidence = 0.9,
                                filepath = getTypeFilepath(param.type)
                            )
                        )
                    }
                }

                is PsiCodeBlock -> {
                    current.statements.forEach { statement ->
                        if (statement is PsiDeclarationStatement) {
                            statement.declaredElements.forEach { declared ->
                                if (declared is PsiLocalVariable) {
                                    objects.add(
                                        ObjectInfo(
                                            name = declared.name,
                                            type = declared.type.presentableText,
                                            qualifiedType = declared.type.canonicalText,
                                            kind = ObjectKind.LOCAL_VARIABLE,
                                            element = declared,
                                            members = extractJavaTypeMembers(declared.type),
                                            confidence = 0.8,
                                            filepath = getTypeFilepath(declared.type)
                                        )
                                    )
                                }
                            }
                        }
                    }
                }
            }
            current = current.parent
        }

        return objects
    }

    /**
     * 推断 Lambda 参数类型
     */
    private fun inferLambdaParameterType(lambdaExpression: PsiLambdaExpression): PsiType? {
        // 尝试从上下文推断类型
        val parent = lambdaExpression.parent
        // 这里可以添加更复杂的类型推断逻辑
        return null
    }

    /**
     * 提取Java类的成员
     */
    fun extractJavaClassMembers(psiClass: PsiClass?): List<MemberInfo> {
        if (psiClass == null) return emptyList()

        val members = mutableListOf<MemberInfo>()

        // 处理 Companion 对象的成员
        psiClass.fields.filter { it.name == "Companion" }.forEach { companion ->
            val companionClass = PsiTypesUtil.getPsiClass(companion.type)
            companionClass?.let { compClass ->
                // 添加 Companion 对象的成员
                compClass.methods.forEach { method ->
                    if (!method.isConstructor) {
                        members.add(
                            MemberInfo(
                                name = "Companion." + method.name,
                                type = method.returnType?.presentableText ?: "void",
                                kind = MemberKind.METHOD,
                                visibility = getJavaVisibility(method),
                                isStatic = true, // Companion 成员在 Java 中表现为静态
                                signature = "Companion." + buildJavaMethodSignature(method),
                                documentation = method.docComment?.text
                            )
                        )
                    }
                }

                compClass.fields.forEach { field ->
                    members.add(
                        MemberInfo(
                            name = "Companion." + field.name,
                            type = field.type.presentableText,
                            kind = MemberKind.FIELD,
                            visibility = getJavaVisibility(field),
                            isStatic = true,
                            documentation = field.docComment?.text
                        )
                    )
                }
            }
        }

        // 添加字段
        psiClass.fields.filterNot { it.name == "Companion" }.forEach { field ->
            members.add(
                MemberInfo(
                    name = field.name,
                    type = field.type.presentableText,
                    kind = MemberKind.FIELD,
                    visibility = getJavaVisibility(field),
                    isStatic = field.hasModifierProperty(PsiModifier.STATIC),
                    documentation = field.docComment?.text
                )
            )
        }

        // 添加方法
        psiClass.methods.forEach { method ->
            members.add(
                MemberInfo(
                    name = method.name,
                    type = method.returnType?.presentableText ?: "void",
                    kind = MemberKind.METHOD,
                    visibility = getJavaVisibility(method),
                    isStatic = method.hasModifierProperty(PsiModifier.STATIC),
                    signature = buildJavaMethodSignature(method),
                    documentation = method.docComment?.text
                )
            )
        }

        return members
    }

    /**
     * 提取Java类型的成员
     */
    private fun extractJavaTypeMembers(type: PsiType): List<MemberInfo> {
        val psiClass = PsiTypesUtil.getPsiClass(type)
        return extractJavaClassMembers(psiClass)
    }

    /**
     * 获取Java可见性
     */
    private fun getJavaVisibility(member: PsiMember): String {
        return when {
            member.hasModifierProperty(PsiModifier.PUBLIC) -> "public"
            member.hasModifierProperty(PsiModifier.PRIVATE) -> "private"
            member.hasModifierProperty(PsiModifier.PROTECTED) -> "protected"
            else -> "package"
        }
    }

    /**
     * 构建Java方法签名
     */
    private fun buildJavaMethodSignature(method: PsiMethod): String {
        val params = method.parameterList.parameters.joinToString(", ") { param ->
            "${param.type.presentableText} ${param.name}"
        }
        return "${method.name}($params): ${method.returnType?.presentableText ?: "void"}"
    }

    private fun getTypeFilepath(psiType: PsiType): String? {
        return (psiType as? PsiClassType)?.resolve()?.containingFile?.virtualFile?.toUriOrNull()
    }
}
