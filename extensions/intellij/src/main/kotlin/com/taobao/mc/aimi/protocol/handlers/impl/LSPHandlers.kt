package com.taobao.mc.aimi.protocol.handlers.impl

import com.google.gson.JsonElement
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.Location
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.protocol.handlers.AbstractMessageHandler
import com.taobao.mc.aimi.types.MessageTypes.ToIDE

/**
 * LSP (Language Server Protocol) 相关的消息处理器
 * 包括跳转到定义、跳转到类型定义、获取签名帮助、获取引用、获取文档符号等功能
 */

/**
 * 跳转到定义处理器
 */
class GotoDefinitionHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GotoDefinition

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val location = parseParams<Location>(dataElement)
        val definitions = ide.gotoDefinition(location)
        respond(definitions)
    }
}

/**
 * 跳转到类型定义处理器
 */
class GotoTypeDefinitionHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GotoTypeDefinition

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val location = parseParams<Location>(dataElement)
        val typeDefinitions = ide.gotoTypeDefinition(location)
        respond(typeDefinitions)
    }
}

/**
 * 获取签名帮助处理器
 */
class GetSignatureHelpHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetSignatureHelp

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val location = parseParams<Location>(dataElement)
        val signatureHelp = ide.getSignatureHelp(location)
        respond(signatureHelp)
    }
}

/**
 * 获取引用处理器
 */
class GetReferencesHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetReferences

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val location = parseParams<Location>(dataElement)
        val references = ide.getReferences(location)
        respond(references)
    }
}

/**
 * 获取文档符号处理器
 */
class GetDocumentSymbolsHandler(
    project: Project,
    ide: IDE,
    aimiService: AIMIPluginService,
    diffStreamService: DiffStreamService
) : AbstractMessageHandler(project, ide, aimiService, diffStreamService) {

    override val supportedMessageType: ToIDE = ToIDE.GetDocumentSymbols

    override suspend fun handle(messageId: String, dataElement: JsonElement, respond: (Any?) -> Unit) {
        val params = parseParams<Map<String, String>>(dataElement)
        val textDocumentIdentifier = params["textDocumentIdentifier"] ?: ""
        val documentSymbols = ide.getDocumentSymbols(textDocumentIdentifier)
        respond(documentSymbols)
    }
}