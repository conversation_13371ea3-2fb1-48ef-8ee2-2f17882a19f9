package com.taobao.mc.aimi.psi

import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.components.service
import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiClass
import com.intellij.psi.PsiElement
import com.intellij.psi.util.PsiTreeUtil
import com.taobao.mc.aimi.ext.utils.toUriOrNull
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.psi.types.*
import org.jetbrains.kotlin.analysis.api.KaExperimentalApi
import org.jetbrains.kotlin.analysis.api.KaSession
import org.jetbrains.kotlin.analysis.api.analyze
import org.jetbrains.kotlin.analysis.api.renderer.declarations.impl.KaDeclarationRendererForSource
import org.jetbrains.kotlin.analysis.api.renderer.types.impl.KaTypeRendererForSource
import org.jetbrains.kotlin.analysis.api.resolution.successfulFunctionCallOrNull
import org.jetbrains.kotlin.analysis.api.resolution.symbol
import org.jetbrains.kotlin.analysis.api.symbols.*
import org.jetbrains.kotlin.analysis.api.types.KaClassType
import org.jetbrains.kotlin.analysis.api.types.KaFunctionType
import org.jetbrains.kotlin.analysis.api.types.KaType
import org.jetbrains.kotlin.analysis.api.types.classSymbol
import org.jetbrains.kotlin.analysis.api.types.symbol
import org.jetbrains.kotlin.lexer.KtTokens
import org.jetbrains.kotlin.psi.*
import org.jetbrains.kotlin.psi.psiUtil.getParentOfType
import org.jetbrains.kotlin.types.Variance

/**
 * K2 模式下的 Kotlin 分析适配器实现
 * 使用新的 Analysis API 和 Symbol API
 *
 * 注意：这个实现需要在支持 K2 的 IntelliJ 版本中才能正常工作
 * 当前实现是一个基础框架，具体的 K2 API 调用需要根据实际的 Analysis API 进行调整
 */
@OptIn(KaExperimentalApi::class)
class K2Resolver : KotlinResolver {

    private val logger = LoggerManager.getLogger(javaClass)
    private val javaResolver by lazy { serviceOrNull<JavaObjectTypeResolver>() }

    override val resolverModel: String
        get() = "Kotlin (K2) Resolver"

    /**
     * 解析 Kotlin 对象
     */
    override fun resolveObjects(element: PsiElement, offset: Int): List<ObjectInfo> {
        // 使用协程在后台线程中执行Analysis API，避免在EDT线程中执行
        return ReadAction.compute<List<ObjectInfo>, RuntimeException> {
            val objects = mutableListOf<ObjectInfo>()

            // 首先检查是否在方法调用的括号内，如果是，找到方法调用的接收者
            val methodCallReceiver = findMethodCallReceiver(element)
            if (methodCallReceiver != null) {
                logger.debug("Found method call receiver: ${methodCallReceiver.text}")
                val receiverInfo = resolveExpressionType(methodCallReceiver as KtExpression)
                if (receiverInfo != null) {
                    objects.add(receiverInfo.copy(confidence = 1.0)) // 最高优先级
                }
            }

            // 检查是否在点号后面
            val dotContext = findKotlinDotContext(element)
            if (dotContext != null) {
                val objectInfo = resolveExpressionType(dotContext)
                if (objectInfo != null) {
                    objects.add(objectInfo)
                }
            } else {
                objects.addAll(findAvailableKotlinObjects(element))
            }

            objects.sortedByDescending { it.confidence }
        }
    }

    override fun resolveLambdaParameters(lambdaExpression: PsiElement): List<ObjectInfo> {
        if (lambdaExpression !is KtLambdaExpression) return emptyList()
        // 使用协程在后台线程中执行Analysis API，避免在EDT线程中执行
        return ReadAction.compute<List<ObjectInfo>, RuntimeException> {
            val objects = mutableListOf<ObjectInfo>()
            try {
                analyze(lambdaExpression) {
                    // 获取 Lambda 参数
                    val valueParameters = lambdaExpression.valueParameters
                    valueParameters.forEach { param ->
                        // 使用K2 API获取参数类型
                        val type = param.symbol.returnType

                        objects.add(
                            ObjectInfo(
                                name = param.name ?: "it",
                                type = renderType(type),
                                qualifiedType = type.symbol?.classId?.asFqNameString(),
                                kind = ObjectKind.LAMBDA_PARAMETER,
                                element = param,
                                members = extractKotlinTypeMembers(type),
                                isNullable = type.isMarkedNullable,
                                confidence = 1.0,
                                isLambdaParameter = true,
                                lambdaContext = "Kotlin Lambda",
                                filepath = getTypeFilePath(type),
                                content = getSpecialTypeSourceCode(type)
                            )
                        )
                    }

                    // 如果没有显式参数，检查是否有隐式 'it' 参数
                    if (objects.isEmpty()) {
                        // 尝试推断 'it' 参数的类型
                        val itType = inferItParameterType(lambdaExpression)
                        if (itType != null) {
                            objects.add(
                                ObjectInfo(
                                    name = "it",
                                    type = renderType(itType),
                                    qualifiedType = getDeclaratorFqName(itType),
                                    kind = ObjectKind.LAMBDA_PARAMETER,
                                    element = lambdaExpression,
                                    members = extractKotlinTypeMembers(itType),
                                    isNullable = itType.isMarkedNullable,
                                    confidence = 0.9,
                                    isLambdaParameter = true,
                                    lambdaContext = "Kotlin Lambda (it parameter)",
                                    filepath = getTypeFilePath(itType),
                                    content = getSpecialTypeSourceCode(itType)
                                )
                            )
                        }
                    }

                    // 检查是否有接收者类型（扩展函数样式的 Lambda）
                    val receiverType = inferLambdaReceiverType(lambdaExpression)
                    if (receiverType != null) {
                        objects.add(
                            ObjectInfo(
                                name = "this",
                                type = renderType(receiverType),
                                qualifiedType = getDeclaratorFqName(receiverType),
                                kind = ObjectKind.LAMBDA_RECEIVER,
                                element = lambdaExpression,
                                members = extractKotlinTypeMembers(receiverType),
                                isNullable = receiverType.isMarkedNullable,
                                confidence = 1.0,
                                isLambdaParameter = false,
                                lambdaContext = "Kotlin Lambda Receiver",
                                filepath = getTypeFilePath(receiverType),
                                content = getSpecialTypeSourceCode(receiverType)
                            )
                        )
                    }
                }
            } catch (e: Exception) {
                logger.debug("Failed to resolve Kotlin lambda parameters", e)
            }

            objects
        }
    }

    /**
     * 查找方法调用的接收者对象
     */
    private fun findMethodCallReceiver(element: PsiElement): PsiElement? {
        var current: PsiElement? = element

        // 向上查找，寻找方法调用表达式
        while (current != null) {
            when (current) {
                // Kotlin方法调用
                is KtCallExpression -> {
                    val parent = current.parent
                    if (parent is KtDotQualifiedExpression) {
                        return parent.receiverExpression
                    }
                }
                // Kotlin安全调用
                is KtSafeQualifiedExpression -> {
                    return current.receiverExpression
                }
            }
            current = current.parent
        }

        return null
    }

    /**
     * 查找 Kotlin 点号上下文
     */
    private fun findKotlinDotContext(element: PsiElement): KtExpression? {
        val parent = element.parent
        if (parent is KtDotQualifiedExpression) {
            return parent.receiverExpression
        }
        return null
    }

    private fun resolveExpressionType(expression: KtExpression): ObjectInfo? {
        return analyze(expression) {
            val expressionType = expression.expressionType
            when (expression) {
                is KtThisExpression -> {
                    val containingClass = PsiTreeUtil.getParentOfType(expression, KtClass::class.java)

                    // 使用K2 API获取this的类型信息
                    val thisSymbol = expressionType?.symbol
                    val classSymbol = thisSymbol?.containingSymbol as? KaClassSymbol

                    ObjectInfo(
                        name = "this",
                        type = containingClass?.name ?: classSymbol?.name?.asString() ?: "Unknown",
                        qualifiedType = classSymbol?.classId?.asFqNameString(),
                        kind = ObjectKind.THIS,
                        element = containingClass,
                        members = containingClass?.let { extractKotlinClassMembers(it) } ?: emptyList(),
                        confidence = 1.0,
                        filepath = containingClass?.containingFile?.virtualFile?.toUriOrNull(),
                        content = expressionType?.let { getSpecialTypeSourceCode(it) }
                    )
                }

                is KtSuperExpression -> {
                    val containingClass = PsiTreeUtil.getParentOfType(expression, KtClass::class.java)
                    val superTypes = containingClass?.superTypeListEntries
                    val superType = superTypes?.firstOrNull()

                    // 使用K2 API解析父类型
                    val resolvedSuperType = superType?.let { entry ->
                        entry.typeReference?.type
                    }

                    val superTypeName = renderType(resolvedSuperType, superType?.text ?: "Any")
                    val qualifiedTypeName = (resolvedSuperType as? KaClassType)?.classId?.asFqNameString()

                    // 提取父类成员
                    val superMembers = resolvedSuperType?.let { type ->
                        extractKotlinTypeMembers(type)
                    } ?: emptyList()

                    // 获取文件路径
                    val filepath = getTypeFilePath(resolvedSuperType)

                    ObjectInfo(
                        name = "super",
                        type = superTypeName,
                        qualifiedType = qualifiedTypeName,
                        kind = ObjectKind.SUPER,
                        element = superType,
                        members = superMembers,
                        confidence = 1.0,
                        filepath = filepath
                    )
                }

                is KtNameReferenceExpression -> {
                    val symbol = expressionType?.symbol
                    val expressionType = expression.expressionType

                    if (expressionType != null) {
                        // 获取更友好的类型名称
                        val typeName = renderType(expressionType, "Any")
                        val variableName = expression.getReferencedName()

                        logger.debug("Resolved Kotlin reference: $variableName -> $typeName")

                        ObjectInfo(
                            name = variableName,
                            type = typeName,
                            qualifiedType = (expressionType as? KaClassType)?.classId?.asFqNameString(),
                            kind = determineKotlinObjectKind(symbol),
                            element = expression,
                            members = extractKotlinTypeMembers(expressionType),
                            isNullable = expressionType.isMarkedNullable,
                            confidence = 0.9,
                            filepath = getTypeFilePath(expressionType),
                            content = getSpecialTypeSourceCode(expressionType)
                        )
                    } else {
                        // 获取符号对应的PSI元素
                        val psiElement = symbol?.psi

                        // 检查具体类型
                        return@analyze when (psiElement) {
                            is KtClass -> {
                                // Kotlin 类
                                val kotlinClass = psiElement
                                val qualifiedName = kotlinClass.fqName?.asString()

                                ObjectInfo(
                                    name = expression.getReferencedName(),
                                    type = kotlinClass.name ?: "Unknown",
                                    qualifiedType = qualifiedName,
                                    kind = determineKotlinObjectKind(symbol),
                                    element = kotlinClass,
                                    members = extractKotlinClassMembers(kotlinClass),
                                    confidence = 0.9,
                                    filepath = kotlinClass.containingFile.virtualFile?.toUriOrNull(),
                                    content = getSpecialTypeSourceCode(null)
                                )
                            }

                            is PsiClass -> {
                                // Java 类 - 委托给 Java 解析器
                                val javaClass = psiElement
                                val qualifiedName = javaClass.qualifiedName

                                ObjectInfo(
                                    name = expression.getReferencedName(),
                                    type = javaClass.name ?: "Unknown",
                                    qualifiedType = qualifiedName,
                                    kind = determineKotlinObjectKind(symbol),
                                    element = javaClass,
                                    members = javaResolver?.extractJavaClassMembers(javaClass) ?: emptyList(),
                                    confidence = 0.9,
                                    filepath = javaClass.containingFile.virtualFile?.toUriOrNull()
                                )
                            }

                            else -> null
                        }
                    }
                }

                else -> {
                    val expressionType = expression.expressionType
                    if (expressionType != null) {
                        ObjectInfo(
                            name = "expression",
                            type = expressionType.symbol?.name?.toString() ?: "Any",
                            qualifiedType = (expressionType as? KaClassType)?.classId?.asFqNameString(),
                            kind = ObjectKind.EXPRESSION,
                            element = expression,
                            members = extractKotlinTypeMembers(expressionType),
                            isNullable = expressionType.isMarkedNullable,
                            confidence = 0.7,
                            filepath = getTypeFilePath(expressionType),
                            content = getSpecialTypeSourceCode(expressionType)
                        )
                    } else null
                }
            }
        }
    }

    private fun KaSession.inferItParameterType(lambdaExpression: KtLambdaExpression): KaType? {
        return run {
            try {
                // 1. 从 K2 Analysis API 获取 lambda 的类型信息
                val lambdaType = lambdaExpression.expressionType
                if (lambdaType != null && lambdaType.isFunctionType) {
                    val functionType = lambdaType as? KaFunctionType
                    if (functionType != null && functionType.parameterTypes.isNotEmpty()) {
                        return@run functionType.parameterTypes.first()
                    }
                }

                // 2. 尝试从父级调用表达式推断
                val parent = lambdaExpression.parent
                if (parent is KtValueArgument) {
                    val callExpression = findLambdaCallContext(lambdaExpression)
                    if (callExpression != null) {
                        val resolvedCall = callExpression.resolveToCall()?.successfulFunctionCallOrNull()
                        if (resolvedCall != null) {
                            // 查找 lambda 参数对应的参数类型
                            val lambdaParam = resolvedCall.symbol.valueParameters.lastOrNull()
                            if (lambdaParam != null) {
                                val paramType = lambdaParam.returnType
                                if (paramType.isFunctionType) {
                                    val functionParamType = paramType as? KaFunctionType
                                    if (functionParamType != null && functionParamType.parameterTypes.isNotEmpty()) {
                                        return@run functionParamType.parameterTypes.first()
                                    }
                                }
                            }
                        }
                    }
                }

                // 3. 尝试从扩展函数的接收者类型推断
                if (parent is KtDotQualifiedExpression) {
                    val receiverExpression = parent.receiverExpression
                    val receiverType = receiverExpression.expressionType
                    if (receiverType != null) {
                        return@run receiverType
                    }
                }

                // 4. 尝试从赋值语句推断
                val assignment = lambdaExpression.getParentOfType<KtBinaryExpression>(false)
                if (assignment?.operationToken == KtTokens.EQ) {
                    val leftExpression = assignment.left
                    if (leftExpression != null) {
                        val leftType = leftExpression.expressionType
                        if (leftType != null && leftType.isFunctionType) {
                            val functionType = leftType as? KaFunctionType
                            if (functionType != null && functionType.parameterTypes.isNotEmpty()) {
                                return@run functionType.parameterTypes.first()
                            }
                        }
                    }
                }

            } catch (e: Exception) {
                logger.debug("Failed to infer 'it' parameter type with K2", e)
            }

            null
        }
    }

    private fun findLambdaCallContext(lambdaExpression: KtLambdaExpression): KtCallExpression? {
        var parent = lambdaExpression.parent

        // 向上查找直到找到函数调用表达式
        while (parent != null) {
            when (parent) {
                is KtCallExpression -> return parent
                is KtDotQualifiedExpression -> {
                    val selectorExpression = parent.selectorExpression
                    if (selectorExpression is KtCallExpression) {
                        return selectorExpression
                    }
                }
            }
            parent = parent.parent
        }
        return null
    }

    private fun KaSession.inferLambdaReceiverType(lambdaExpression: KtLambdaExpression): KaType? {
        // 尝试推断 Lambda 的接收者类型（用于扩展函数样式的 Lambda）
        try {
            return run {
                // 从调用上下文推断接收者类型
                val callExpression = findLambdaCallContext(lambdaExpression)
                if (callExpression != null) {
                    val receiverType = inferReceiverTypeFromCallContext(callExpression)
                    if (receiverType != null) {
                        logger.debug("Inferred lambda receiver type: ${renderType(receiverType)}")
                        return receiverType
                    }
                }

                // 尝试从Lambda表达式的函数类型中推断接收者类型
                val lambdaType = lambdaExpression.expressionType
                lambdaType?.let {
                    val receiverType = extractReceiverTypeFromFunctionType(lambdaType)
                    if (receiverType != null) {
                        logger.debug("Extracted receiver type from function type: ${renderType(receiverType)}")
                        return receiverType
                    }
                }

                null
            }
        } catch (e: Exception) {
            logger.debug("Failed to infer lambda receiver type", e)
        }
        return null
    }


    /**
     * 从调用上下文推断接收者类型
     */
    private fun KaSession.inferReceiverTypeFromCallContext(callExpression: KtCallExpression): KaType? {
        try {
            val calleeExpression = callExpression.calleeExpression
            if (calleeExpression is KtNameReferenceExpression) {
                val functionName = calleeExpression.getReferencedName()

                // 处理标准库的作用域函数
                when (functionName) {
                    "apply", "run" -> {
                        // apply和run函数的Lambda接收者类型是调用者类型
                        return getReceiverTypeFromQualifiedExpression(callExpression)
                    }

                    "with" -> {
                        // with函数的Lambda接收者类型是第一个参数类型
                        val firstArg = callExpression.valueArguments.firstOrNull()?.getArgumentExpression()
                        if (firstArg != null) {
                            return firstArg.expressionType
                        }
                    }
                }
            }

            // 尝试从解析的函数描述符获取接收者类型
            val resolvedCall = callExpression.resolveToCall()?.successfulFunctionCallOrNull()
            if (resolvedCall != null) {
                // 查找 lambda 参数对应的参数类型
                val lambdaParam = resolvedCall.symbol.valueParameters.lastOrNull()
                if (lambdaParam != null) {
                    val paramType = lambdaParam.returnType
                    if (paramType.isFunctionType) {
                        // 检查是否是带接收者的函数类型
                        return extractReceiverTypeFromFunctionType(paramType)
                    }
                }
            }

        } catch (e: Exception) {
            logger.debug("Failed to infer receiver type from call context", e)
        }
        return null
    }

    /**
     * 从限定表达式获取接收者类型
     */
    private fun KaSession.getReceiverTypeFromQualifiedExpression(callExpression: KtCallExpression): KaType? {
        val parent = callExpression.parent
        if (parent is KtDotQualifiedExpression) {
            val receiverExpression = parent.receiverExpression
            return receiverExpression.expressionType
        }
        return null
    }

    /**
     * 从函数类型中提取接收者类型
     */
    private fun extractReceiverTypeFromFunctionType(functionType: KaType): KaType? {
        try {
            // 检查是否是扩展函数类型（带接收者的函数类型）
            if (functionType is KaFunctionType && functionType.hasReceiver) {
                // functionType.typeArguments
                // 对于扩展函数类型 T.() -> R，第一个类型参数是接收者类型T
                functionType.parameterTypes.firstOrNull()
                    ?.takeIf { isExtensionFunctionType(it) }
                    ?.let {
                        return it
                    }
            }
        } catch (e: Exception) {
            logger.debug("Failed to extract receiver type from function type", e)
        }
        return null
    }

    /**
     * 检查是否是扩展函数类型
     */
    private fun isExtensionFunctionType(type: KaType): Boolean {
        try {
            // 在Kotlin类型系统中，扩展函数类型有特殊的标记
            // 可以通过检查类型的注解或其他属性来判断
            return type.annotations.any { annotation ->
                annotation.classId?.asFqNameString() == "kotlin.ExtensionFunctionType"
            }
        } catch (e: Exception) {
            logger.debug("Failed to check if extension function type", e)
            return false
        }
    }

    private fun KaSession.extractKotlinClassMembers(ktClass: KtClass): List<MemberInfo> {
        val members = mutableListOf<MemberInfo>()

        return run {
            // 添加属性
            ktClass.getProperties().forEach { property ->
                val propertySymbol = property.symbol
                val propertyType = propertySymbol.returnType
                members.add(
                    MemberInfo(
                        name = property.name ?: "",
                        type = renderType(propertyType),
                        kind = MemberKind.PROPERTY,
                        visibility = getKotlinVisibility(property),
                        documentation = property.docComment?.text
                    )
                )
            }

            // 添加函数
            ktClass.declarations.filterIsInstance<KtNamedFunction>().forEach { function ->
                val functionSymbol = function.symbol
                val returnType = functionSymbol.returnType
                members.add(
                    MemberInfo(
                        name = function.name ?: "",
                        type = renderType(returnType, "Unit"),
                        kind = MemberKind.METHOD,
                        visibility = getKotlinVisibility(function),
                        signature = buildKotlinMethodSignature(function),
                        documentation = function.docComment?.text
                    )
                )
            }

            // 添加伴生对象中的静态成员
            ktClass.getCompanionObjects().forEach { companionObject ->
                companionObject.declarations.filterIsInstance<KtNamedFunction>().forEach { function ->
                    val functionSymbol = function.symbol
                    val returnType = functionSymbol.returnType
                    members.add(
                        MemberInfo(
                            name = "Companion." + (function.name ?: "unknown"),
                            type = renderType(returnType, "Unit"),
                            kind = MemberKind.METHOD,
                            visibility = getKotlinVisibility(function),
                            signature = "Companion." + buildKotlinMethodSignature(function),
                            documentation = function.docComment?.text,
                            isStatic = true
                        )
                    )
                }
                companionObject.declarations.filterIsInstance<KtProperty>().forEach { property ->
                    val propertySymbol = property.symbol
                    val propertyType = propertySymbol.returnType
                    members.add(
                        MemberInfo(
                            name = "Companion." + (property.name ?: "unknown"),
                            type = renderType(propertyType),
                            kind = MemberKind.PROPERTY,
                            visibility = getKotlinVisibility(property),
                            documentation = property.docComment?.text
                        )
                    )
                }
            }

            members
        }
    }

    private fun KaSession.extractKotlinTypeMembers(kaType: KaType): List<MemberInfo> {
        val members = mutableListOf<MemberInfo>()
        val symbol = kaType.symbol
        if (symbol == null) return members

        try {
            // 需要一个 KtElement 来进行分析，这里需要从 typeInfo 中获取
            // 或者通过其他方式获取合适的 KtElement
            // 这需要根据具体情况实现
            // val ktElement = getKtElementFromKaType(kaType) ?: return members

            run {
                val classSymbol = symbol as? KaClassSymbol
                classSymbol?.let { symbol ->
                    // 获取所有成员
                    val memberSymbols = symbol.memberScope.declarations

                    memberSymbols.forEach { memberSymbol ->
                        when (memberSymbol) {
                            is KaPropertySymbol -> {
                                members.add(
                                    MemberInfo(
                                        name = memberSymbol.name.asString(),
                                        type = renderType(memberSymbol.returnType),
                                        kind = MemberKind.PROPERTY,
                                        isStatic = memberSymbol.isStatic,
                                        visibility = memberSymbol.visibility.name.lowercase()
                                    )
                                )
                            }

                            is KaFunctionSymbol -> {

                                members.add(
                                    MemberInfo(
                                        name = memberSymbol.name?.asString() ?: "unknown",
                                        type = renderType(memberSymbol.returnType),
                                        kind = MemberKind.METHOD,
                                        visibility = memberSymbol.visibility.name.lowercase(),
                                        isStatic = false,
                                        signature = "fun ${memberSymbol.render(KaDeclarationRendererForSource.WITH_SHORT_NAMES).substringAfter("fun ")}"
                                    )
                                )
                            }

                            else -> {}
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logger.debug("Failed to extract Kotlin type members", e)
        }

        return members
    }

    private fun getTypeFilePath(kaType: KaType?): String? {
        kaType ?: return null
        return try {
            val ktElement = getKtElementFromKaType(kaType) ?: return null
            ktElement.containingFile?.virtualFile?.toUriOrNull()
        } catch (e: Exception) {
            logger.debug("Failed to get type file path", e)
            null
        }
    }

    /**
     * 从 KaType 获取对应的 KtElement
     */
    private fun getKtElementFromKaType(kaType: KaType): KtElement? {
        return when (val symbol = kaType.symbol) {
            is KaClassSymbol -> symbol.psi as? KtClass
            is KaTypeAliasSymbol -> symbol.psi as? KtTypeAlias
            else -> symbol?.psi as? KtElement
        }
    }

    private fun getSpecialTypeSourceCode(kaType: KaType?): String? {
        kaType ?: return null
        return try {
            val ktElement = getKtElementFromKaType(kaType)
            (ktElement as? KtClass)?.takeIf {
                // 检查是否为密封类、数据类或枚举类
                it.isData() || it.isEnum() || it.isSealed()
            }?.text
        } catch (e: Exception) {
            logger.debug("Failed to get special type source code", e)
            null
        }
    }

    private fun KaSession.renderType(kaType: KaType?, ifNull: String = "Any", shortName: Boolean = true): String {
        kaType ?: return ifNull
        val renderer = if (shortName) KaTypeRendererForSource.WITH_SHORT_NAMES else KaTypeRendererForSource.WITH_QUALIFIED_NAMES
        return kaType.render(renderer, Variance.INVARIANT)
    }

    private fun findAvailableKotlinObjects(element: PsiElement): List<ObjectInfo> {
        val objects = mutableListOf<ObjectInfo>()

        // 添加this对象
        val containingClass = PsiTreeUtil.getParentOfType(element, KtClass::class.java)
        val containingFunction = PsiTreeUtil.getParentOfType(element, KtNamedFunction::class.java)

        if (containingClass != null) {
            analyze(containingClass) {
                val classSymbol = containingClass.classSymbol
                objects.add(
                    ObjectInfo(
                        name = "this",
                        type = containingClass.name ?: "Unknown",
                        qualifiedType = classSymbol?.classId?.asFqNameString(),
                        kind = ObjectKind.THIS,
                        element = containingClass,
                        members = extractKotlinClassMembers(containingClass),
                        confidence = 1.0,
                        filepath = containingClass.containingFile.virtualFile?.toUriOrNull(),
                        content = containingClass.takeIf { it.isData() || it.isSealed() || it.isEnum() }?.text
                    )
                )
            }
        }

        // 添加函数参数
        containingFunction?.let {
            analyze(it) {
                containingFunction.valueParameters.forEach { param ->
                    try {
                        val paramSymbol = param.symbol
                        val paramType = paramSymbol.returnType

                        objects.add(
                            ObjectInfo(
                                name = param.name ?: "",
                                type = paramType.render(KaTypeRendererForSource.WITH_SHORT_NAMES, position = Variance.INVARIANT),
                                qualifiedType = paramType.expandedSymbol?.classId?.asFqNameString(),
                                kind = ObjectKind.PARAMETER,
                                element = param,
                                members = extractKotlinTypeMembers(paramType),
                                isNullable = paramType.isMarkedNullable,
                                confidence = 0.9,
                                filepath = getTypeFilePath(paramType),
                                content = getSpecialTypeSourceCode(paramType)
                            )
                        )
                    } catch (e: Exception) {
                        logger.debug("Failed to analyze Kotlin parameter", e)
                    }
                }
            }
        }

        // 添加局部变量
        var current: PsiElement? = element
        while (current != null) {
            if (current is KtBlockExpression) {
                analyze(current) {
                    current.statements.forEach { statement ->
                        if (statement is KtProperty) {
                            try {
                                val propertySymbol = statement.symbol
                                val propertyType = propertySymbol.returnType

                                objects.add(
                                    ObjectInfo(
                                        name = statement.name ?: "",
                                        type = renderType(propertyType, "Any"),
                                        qualifiedType = renderType(propertyType, propertyType.symbol?.name?.asString() ?: "Any", false),
                                        kind = ObjectKind.LOCAL_VARIABLE,
                                        element = statement,
                                        members = extractKotlinTypeMembers(propertyType),
                                        isNullable = propertyType.isMarkedNullable,
                                        confidence = 0.8,
                                        filepath = getTypeFilePath(propertyType),
                                        content = getSpecialTypeSourceCode(propertyType)
                                    )
                                )
                            } catch (e: Exception) {
                                logger.debug("Failed to analyze Kotlin property", e)
                            }
                        }
                    }
                }
            }
            current = current.parent
        }
        return objects
    }

    private fun KaSession.buildKotlinMethodSignature(function: KtNamedFunction): String {
        return runCatching {
            "fun ${function.symbol.render(KaDeclarationRendererForSource.WITH_SHORT_NAMES).substringAfter("fun ")}"
        }.getOrNull() ?: run {
            // 回退到原始实现
            val params = function.valueParameters.joinToString(", ") { param ->
                val symbol = param.symbol
                "${symbol.name}: ${renderType(param.returnType)}"
            }
            "${function.name}($params): ${renderType(function.symbol.returnType, "Unit")}"
        }
    }

    private fun getKotlinVisibility(member: KtModifierListOwner): String {
        // 这个方法不依赖于 K1/K2，可以直接实现
        return when {
            member.hasModifier(KtTokens.PUBLIC_KEYWORD) -> "public"
            member.hasModifier(KtTokens.PRIVATE_KEYWORD) -> "private"
            member.hasModifier(KtTokens.PROTECTED_KEYWORD) -> "protected"
            member.hasModifier(KtTokens.INTERNAL_KEYWORD) -> "internal"
            else -> "public"
        }
    }

    private fun determineKotlinObjectKind(resolved: KaSymbol?): ObjectKind {
        return when (resolved) {
            is KaLocalVariableSymbol -> ObjectKind.LOCAL_VARIABLE
            is KaValueParameterSymbol -> ObjectKind.PARAMETER
            is KaPropertySymbol -> ObjectKind.FIELD
            else -> ObjectKind.LOCAL_VARIABLE
        }
    }

    private fun getDeclaratorFqName(type: KaType): String? {
        return type.symbol?.classId?.asFqNameString()
    }
}