package com.taobao.mc.aimi.protocol.handlers

import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.protocol.handlers.impl.*
import com.taobao.mc.aimi.types.MessageTypes
import com.taobao.mc.aimi.types.MessageTypes.ToIDE
import kotlin.reflect.KClass

/**
 * 消息处理器工厂
 * 负责创建和管理所有消息处理器实例
 */
class MessageHandlerFactory(
    private val project: Project,
    private val ide: IDE,
    private val aimiService: AIMIPluginService,
    private val diffStreamService: DiffStreamService
) {
    private val logger = LoggerManager.getLogger(javaClass)

    private val handlers: Map<ToIDE, MessageHandler> by lazy {
        createHandlers()
    }

    /**
     * 获取指定消息类型的处理器
     */
    fun getHandler(messageType: ToIDE): MessageHandler? {
        return handlers[messageType]
    }

    /**
     * 创建所有消息处理器实例
     * 按功能分类组织，便于维护和管理
     */
    private fun createHandlers(): Map<ToIDE, MessageHandler> {
        val handlerClasses = listOf(
            // 基础信息相关 (BasicHandlers.kt)
            GetIdeSettingsHandler::class,
            GetIdeInfoHandler::class,
            IsWorkspaceRemoteHandler::class,
            GetUniqueIdHandler::class,

            // 认证相关 (AuthHandlers.kt)
            SetControlPlaneSessionInfoHandler::class,
            GetControlPlaneSessionInfoHandler::class,
            LogoutOfControlPlaneHandler::class,
            IsTelemetryEnabledHandler::class,

            // 文件操作相关 (FileOperationHandlers.kt)
            ShowFileHandler::class,
            ReadFileHandler::class,
            WriteFileHandler::class,
            DeleteFileHandler::class,
            SaveFileHandler::class,
            FileExistsHandler::class,
            OpenFileHandler::class,
            ShowVirtualFileHandler::class,
            ReadRangeInFileHandler::class,

            // JetBrains 特定功能 (JetbrainsHandlers.kt)
            JetbrainsIsOSREnabledHandler::class,
            JetbrainsGetColorsHandler::class,
            JetbrainsOnLoadHandler::class,
            JetbrainsChangeTitleHandler::class,
            JetbrainsChangeWindowHandler::class,
            JetbrainsToggleAIMIShortcutsHandler::class,

            // LSP相关 (LSPHandlers.kt)
            GotoDefinitionHandler::class,
            GotoTypeDefinitionHandler::class,
            GetSignatureHelpHandler::class,
            GetReferencesHandler::class,
            GetDocumentSymbolsHandler::class,

            // 编辑器相关 (EditorHandlers.kt)
            ShowDiffHandler::class,
            AcceptDiffHandler::class,
            RejectDiffHandler::class,
            ApplyToFileMessageHandler::class,
            InsertAtCursorHandler::class,
            GetOpenFilesHandler::class,
            GetCurrentFileHandler::class,
            GetPinnedFilesHandler::class,
            ShowLinesHandler::class,

            // 工作区和项目相关 (WorkspaceHandlers.kt)
            GetWorkspaceDirsHandler::class,
            GetTerminalContentsHandler::class,
            GetTagsHandler::class,
            GetProblemsHandler::class,
            ListDirHandler::class,
            GetFileStatsHandler::class,

            // Git 相关 (GitHandlers.kt)
            GetGitRootPathHandler::class,
            GetBranchHandler::class,
            GetRepoNameHandler::class,
            GetDiffHandler::class,

            // 搜索相关 (SearchHandlers.kt)
            GetSearchResultsHandler::class,
            GetFileResultsHandler::class,
            QuerySymbolInfoHandler::class,

            // 系统操作相关 (SystemHandlers.kt)
            GetClipboardContentHandler::class,
            CopyTextHandler::class,
            RunCommandHandler::class,
            SubprocessHandler::class,
            ShowToastHandler::class,
            ShowProgressHandler::class,
            CloseSidebarHandler::class,
            OpenUrlHandler::class,
            ToggleDevToolsHandler::class,
            ShowTutorialHandler::class,
            IndexProgressHandler::class,
            GetLoginInfoHandler::class,
            SetLoginInfoHandler::class
        )

        val handlerList = handlerClasses.map { createHandler(it) }

        val toIDES = handlerList.map { it.supportedMessageType }.toSet()
        val remaining = MessageTypes.ideMessageTypes - toIDES
        if (remaining.isNotEmpty()) {
            logger.warn("The following message types are not implemented: $remaining")
        }
        return handlerList.associateBy { it.supportedMessageType }
    }

    private fun createHandler(handlerClass: KClass<out MessageHandler>): MessageHandler {
        return try {
            val constructor = handlerClass.java.getDeclaredConstructor(
                Project::class.java,
                IDE::class.java,
                AIMIPluginService::class.java,
                DiffStreamService::class.java
            )
            constructor.newInstance(project, ide, aimiService, diffStreamService)
        } catch (e: Exception) {
            logger.error("Failed to create handler: ${handlerClass.simpleName}", e)
            throw IllegalStateException("Cannot create handler: ${handlerClass.simpleName}", e)
        }
    }
}
// 注解方式注册
// @Target(AnnotationTarget.CLASS)
// @Retention(AnnotationRetention.RUNTIME)
// annotation class MessageHandlerComponent(val category: String = "")