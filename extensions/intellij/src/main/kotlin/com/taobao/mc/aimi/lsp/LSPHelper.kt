package com.taobao.mc.aimi.lsp

import com.intellij.codeInsight.navigation.actions.GotoDeclarationAction
import com.intellij.codeInsight.navigation.actions.GotoTypeDeclarationAction
import com.intellij.openapi.application.EDT
import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.PsiElement
import com.intellij.psi.search.searches.ReferencesSearch
import com.intellij.psi.util.PsiTreeUtil
import com.intellij.util.Processor
import com.taobao.mc.aimi.ext.*
import com.taobao.mc.aimi.ext.core.UriUtils
import com.taobao.mc.aimi.ext.utils.toUriOrNull
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.util.mSelectedTextEditor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

object LSPHelper {
    private val logger = LoggerManager.getLogger(javaClass)

    /**
     * 跳转到定义
     * @param project 项目实例
     * @param location 位置信息
     * @return 定义位置列表
     */
    suspend fun gotoDefinition(project: Project, location: Location): List<RangeInFile> {
        return withContext(Dispatchers.EDT) {
            try {
                val virtualFile = UriUtils.uriToVirtualFile(location.filepath) ?: return@withContext emptyList()

                val document = FileDocumentManager.getInstance().getDocument(virtualFile) ?: return@withContext emptyList()

                val editor = getEditor(project, virtualFile) ?: return@withContext emptyList()

                val offset = getOffsetFromPosition(document, location.position)

                // 使用 IntelliJ 的 GotoDeclarationAction 来获取定义
                val targets = ReadAction.compute<Array<PsiElement>, Throwable> {
                    GotoDeclarationAction.findAllTargetElements(project, editor, offset)
                }

                convertPsiElementsToRangeInFiles(targets)
            } catch (e: Exception) {
                logger.warn("Error in gotoDefinition", e)
                emptyList()
            }
        }
    }

    /**
     * 跳转到类型定义
     * @param project 项目实例
     * @param location 位置信息
     * @return 类型定义位置列表
     */
    suspend fun gotoTypeDefinition(project: Project, location: Location): List<RangeInFile> {
        return withContext(Dispatchers.EDT) {
            try {
                val virtualFile = UriUtils.uriToVirtualFile(location.filepath) ?: return@withContext emptyList()

                val editor = getEditor(project, virtualFile) ?: return@withContext emptyList()

                val document = FileDocumentManager.getInstance().getDocument(virtualFile) ?: return@withContext emptyList()

                val offset = getOffsetFromPosition(document, location.position)

                // 使用 IntelliJ 的 GotoTypeDeclarationAction 来获取类型定义
                val targets = ReadAction.compute<Array<PsiElement>, Throwable> {
                    GotoTypeDeclarationAction.findSymbolType(editor, offset)?.let { arrayOf(it) } ?: emptyArray()
                }

                convertPsiElementsToRangeInFiles(targets)
            } catch (e: Exception) {
                logger.warn("Error in gotoTypeDefinition", e)
                emptyList()
            }
        }
    }

    /**
     * 获取签名帮助
     * @param project 项目实例
     * @param location 位置信息
     * @return 签名帮助信息
     */
    suspend fun getSignatureHelp(project: Project, location: Location): SignatureHelp? {
        return withContext(Dispatchers.EDT) {
            try {
                val virtualFile = UriUtils.uriToVirtualFile(location.filepath) ?: return@withContext null

                val document = FileDocumentManager.getInstance().getDocument(virtualFile) ?: return@withContext null

                val psiFile = PsiDocumentManager.getInstance(project).getPsiFile(document) ?: return@withContext null

                val offset = getOffsetFromPosition(document, location.position)

                ReadAction.compute<SignatureHelp?, Throwable> {
                    val element = psiFile.findElementAt(offset) ?: return@compute null

                    // 查找方法调用或函数调用
                    val callElement = PsiTreeUtil.getParentOfType(
                        element,
                        com.intellij.psi.PsiCall::class.java,
                        org.jetbrains.kotlin.psi.KtCallExpression::class.java
                    ) ?: return@compute null

                    // 这里简化实现，实际应该根据具体的语言和上下文来获取签名信息
                    // 由于IntelliJ的签名帮助API比较复杂，这里提供一个基础实现
                    val signatures = listOf(
                        SignatureInformation(
                            label = callElement.text,
                            parameters = emptyList(),
                            activeParameter = null
                        )
                    )

                    SignatureHelp(
                        signatures = signatures,
                        activeSignature = 0,
                        activeParameter = 0
                    )
                }
            } catch (e: Exception) {
                logger.warn("Error in getSignatureHelp", e)
                null
            }
        }
    }

    /**
     * 获取引用
     * @param project 项目实例
     * @param location 位置信息
     * @return 引用位置列表
     */
    suspend fun getReferences(project: Project, location: Location): List<RangeInFile> {
        return withContext(Dispatchers.EDT) {
            try {
                val virtualFile = UriUtils.uriToVirtualFile(location.filepath) ?: return@withContext emptyList()

                val document = FileDocumentManager.getInstance().getDocument(virtualFile) ?: return@withContext emptyList()

                val psiFile = PsiDocumentManager.getInstance(project).getPsiFile(document) ?: return@withContext emptyList()

                val offset = getOffsetFromPosition(document, location.position)

                ReadAction.compute<List<RangeInFile>, Throwable> {
                    val element = psiFile.findElementAt(offset) ?: return@compute emptyList()

                    // 找到可引用的元素
                    val targetElement = element.parent ?: element

                    val references = mutableListOf<RangeInFile>()

                    // 使用 ReferencesSearch 查找所有引用
                    ReferencesSearch.search(targetElement).forEach(Processor { reference ->
                        try {
                            val refElement = reference.element
                            val refFile = refElement.containingFile
                            val refVirtualFile = refFile.virtualFile

                            if (refVirtualFile != null) {
                                val refDocument = FileDocumentManager.getInstance().getDocument(refVirtualFile)
                                if (refDocument != null) {
                                    val textRange = reference.rangeInElement.shiftRight(refElement.textRange.startOffset)
                                    val startOffset = textRange.startOffset
                                    val endOffset = textRange.endOffset

                                    val startLine = refDocument.getLineNumber(startOffset)
                                    val startCharacter = startOffset - refDocument.getLineStartOffset(startLine)
                                    val endLine = refDocument.getLineNumber(endOffset)
                                    val endCharacter = endOffset - refDocument.getLineStartOffset(endLine)

                                    references.add(
                                        RangeInFile(
                                            filepath = refVirtualFile.toUriOrNull() ?: return@Processor true,
                                            range = Range(
                                                start = Position(line = startLine, character = startCharacter),
                                                end = Position(line = endLine, character = endCharacter)
                                            )
                                        )
                                    )
                                }
                            }
                        } catch (e: Exception) {
                            logger.warn("Error processing reference", e)
                        }
                        true
                    })

                    references
                }
            } catch (e: Exception) {
                logger.warn("Error in getReferences", e)
                emptyList()
            }
        }
    }

    /**
     * 获取文档符号
     * @param project 项目实例
     * @param textDocumentIdentifier 文档标识符（URI）
     * @return 文档符号列表
     */
    suspend fun getDocumentSymbols(project: Project, textDocumentIdentifier: String): List<DocumentSymbol> {
        return withContext(Dispatchers.EDT) {
            try {
                val virtualFile = UriUtils.uriToVirtualFile(textDocumentIdentifier) ?: return@withContext emptyList()

                val document = FileDocumentManager.getInstance().getDocument(virtualFile) ?: return@withContext emptyList()

                val psiFile = PsiDocumentManager.getInstance(project).getPsiFile(document) ?: return@withContext emptyList()

                ReadAction.compute<List<DocumentSymbol>, Throwable> {
                    val symbols = mutableListOf<DocumentSymbol>()

                    // 遍历PSI树，收集符号信息
                    psiFile.accept(object : com.intellij.psi.PsiRecursiveElementVisitor() {
                        override fun visitElement(element: PsiElement) {
                            super.visitElement(element)

                            // 根据元素类型判断是否为符号
                            val symbolInfo = getSymbolInfo(element, document)
                            if (symbolInfo != null) {
                                symbols.add(symbolInfo)
                            }
                        }
                    })

                    symbols
                }
            } catch (e: Exception) {
                logger.warn("Error in getDocumentSymbols", e)
                emptyList()
            }
        }
    }

    /**
     * 从PSI元素获取符号信息
     */
    private fun getSymbolInfo(element: PsiElement, document: com.intellij.openapi.editor.Document): DocumentSymbol? {
        return try {
            when (element) {
                is com.intellij.psi.PsiClass -> {
                    createDocumentSymbol(element, "Class", document)
                }

                is com.intellij.psi.PsiMethod -> {
                    createDocumentSymbol(element, "Method", document)
                }

                is com.intellij.psi.PsiField -> {
                    createDocumentSymbol(element, "Field", document)
                }

                is org.jetbrains.kotlin.psi.KtClass -> {
                    createDocumentSymbol(element, "Class", document)
                }

                is org.jetbrains.kotlin.psi.KtFunction -> {
                    createDocumentSymbol(element, "Function", document)
                }

                is org.jetbrains.kotlin.psi.KtProperty -> {
                    createDocumentSymbol(element, "Property", document)
                }

                else -> null
            }
        } catch (e: Exception) {
            logger.warn("Error getting symbol info for element: ${element.javaClass.simpleName}", e)
            null
        }
    }

    /**
     * 创建文档符号
     */
    private fun createDocumentSymbol(element: PsiElement, kind: String, document: com.intellij.openapi.editor.Document): DocumentSymbol? {
        return try {
            val textRange = element.textRange
            val startOffset = textRange.startOffset
            val endOffset = textRange.endOffset

            val startLine = document.getLineNumber(startOffset)
            val startCharacter = startOffset - document.getLineStartOffset(startLine)
            val endLine = document.getLineNumber(endOffset)
            val endCharacter = endOffset - document.getLineStartOffset(endLine)

            val range = Range(
                start = Position(line = startLine, character = startCharacter),
                end = Position(line = endLine, character = endCharacter)
            )

            // 获取元素名称
            val name = runCatching {
                @Suppress("KotlinConstantConditions")
                when (element) {
                    is com.intellij.psi.PsiNamedElement -> element.name ?: "Unknown"
                    is org.jetbrains.kotlin.psi.KtNamedDeclaration -> element.name ?: "Unknown"
                    else -> element.text.take(50) // 截取前50个字符作为名称
                }
            }.getOrNull() ?: "Unknown"

            DocumentSymbol(
                name = name,
                kind = kind,
                range = range,
                selectionRange = range // 简化实现，使用相同的范围
            )
        } catch (e: Exception) {
            logger.warn("Error creating document symbol", e)
            null
        }
    }

    /**
     * 将位置信息转换为文档偏移量
     * @param document 文档
     * @param position 位置信息
     * @return 偏移量
     */
    private fun getOffsetFromPosition(document: com.intellij.openapi.editor.Document, position: Position): Int {
        val line = position.line.coerceAtLeast(0).coerceAtMost(document.lineCount - 1)
        val lineStartOffset = document.getLineStartOffset(line)
        val lineEndOffset = document.getLineEndOffset(line)
        val character = position.character.coerceAtLeast(0)

        return (lineStartOffset + character).coerceAtMost(lineEndOffset)
    }

    /**
     * 将 PSI 元素转换为 RangeInFile 列表
     * @param elements PSI 元素数组
     * @return RangeInFile 列表
     */
    private fun convertPsiElementsToRangeInFiles(elements: Array<PsiElement>): List<RangeInFile> {
        return elements.mapNotNull { element ->
            try {
                val containingFile = element.containingFile ?: return@mapNotNull null
                val virtualFile = containingFile.virtualFile ?: return@mapNotNull null
                val document = FileDocumentManager.getInstance().getDocument(virtualFile) ?: return@mapNotNull null

                val textRange = element.textRange
                val startOffset = textRange.startOffset
                val endOffset = textRange.endOffset

                val startLine = document.getLineNumber(startOffset)
                val startCharacter = startOffset - document.getLineStartOffset(startLine)
                val endLine = document.getLineNumber(endOffset)
                val endCharacter = endOffset - document.getLineStartOffset(endLine)

                RangeInFile(
                    filepath = virtualFile.toUriOrNull() ?: return@mapNotNull null, range = Range(
                        start = Position(line = startLine, character = startCharacter), end = Position(line = endLine, character = endCharacter)
                    )
                )
            } catch (e: Exception) {
                logger.warn("Error converting PSI element to RangeInFile", e)
                null
            }
        }
    }

    /**
     * 获取编辑器实例
     * @param project 项目实例
     * @param virtualFile 虚拟文件
     * @return 编辑器实例
     */
    private fun getEditor(project: Project, virtualFile: VirtualFile): Editor? {
        return FileEditorManager.getInstance(project).mSelectedTextEditor ?: EditorFactory.getInstance().getEditors(
            FileDocumentManager.getInstance().getDocument(virtualFile) ?: return null
        ).firstOrNull()
    }
}