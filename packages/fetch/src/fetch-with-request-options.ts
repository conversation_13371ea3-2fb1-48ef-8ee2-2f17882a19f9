import { RequestOptions } from "@continuedev/config-types";
import { RequestInit, Response } from "node-fetch";
import { Pool } from "undici";
import { <PERSON><PERSON><PERSON> } from "buffer";
import { logError, logRequest, logResponse } from "./fetch.js";

// Create a global pool map to manage connection pools
const poolMap = new Map<string, Pool>();

function getPool(origin: string): Pool {
  if (!poolMap.has(origin)) {
    const timeoutSeconds = 7200 * 1000; // 2 hours
    const pool = new Pool(origin, {
      allowH2: true,
      connections: 100,
      connectTimeout: timeoutSeconds,
      keepAliveTimeout: timeoutSeconds,
      keepAliveMaxTimeout: timeoutSeconds,
      keepAliveTimeoutThreshold: 1000,
      pipelining: 1,
      connect: {
        allowH2: true,
        keepAlive: true,
        maxCachedSessions: 256,
        sessionTimeout: timeoutSeconds,
      },
    });

    poolMap.set(origin, pool);

    // 改进的 disconnect 处理
    pool.on("disconnect", () => {
      try {
        console.debug(`[fetch] undici pool disconnected for ${origin}`);
        // 延迟删除，给连接池一些恢复时间
        setTimeout(() => {
          // 再次检查连接池状态，只有在确实无法使用时才删除
          if (poolMap.get(origin) === pool) {
            poolMap.delete(origin);
            console.log(`[fetch] undici pool removed from cache for ${origin}`);
          }
        }, 5000); // 5秒延迟
      } catch (error) {
        console.error(
          `[fetch] Error handling disconnect for ${origin}:`,
          error,
        );
      }
    });

    // 监听其他有用的事件
    pool.on("connect", () => {
      console.log(`[fetch] undici pool connected for ${origin}`);
    });
  }
  return poolMap.get(origin)!;
}

async function fetchWithRequestOptions(
  url_: URL | string,
  init?: RequestInit,
  requestOptions?: RequestOptions,
): Promise<Response> {
  const url = typeof url_ === "string" ? new URL(url_) : url_;
  const shouldLog = url.pathname?.endsWith("completions");

  // Replace localhost with 127.0.0.1
  if (url.hostname === "localhost") {
    url.hostname = "127.0.0.1";
  }

  // Don't use proxy for undici, handle it differently
  let headers: { [key: string]: string } = {};
  for (const [key, value] of Object.entries(init?.headers || {})) {
    headers[key] = value as string;
  }
  headers = {
    ...headers,
    ...requestOptions?.headers,
  };

  // Add extra body properties if provided
  let updatedBody: string | undefined = undefined;
  try {
    if (requestOptions?.extraBodyProperties && typeof init?.body === "string") {
      const parsedBody = JSON.parse(init.body);
      updatedBody = JSON.stringify({
        ...parsedBody,
        ...requestOptions.extraBodyProperties,
      });
    }
  } catch (e) {
    console.debug("Unable to parse HTTP request body: ", e);
  }

  const finalBody = updatedBody ?? init?.body;
  const method = init?.method || "GET";

  // Verbose logging for debugging - log request details
  if (process.env.VERBOSE_FETCH) {
    logRequest(method, url, headers, finalBody);
  }

  // Create origin for pool
  const origin = `${url.protocol}//${url.host}`;

  // Get or create pool for this origin
  const pool = getPool(origin);

  // Prepare request options for undici
  const undiciOptions: any = {
    path: url.pathname + url.search,
    method,
    headers,
    body: finalBody as string | Buffer | null,
    signal: init?.signal,
  };

  try {
    // Use undici pool to make the request
    const resp = await pool.request(undiciOptions);

    // Convert undici response to node-fetch Response format
    // Create headers object compatible with node-fetch
    const responseHeaders: [string, string][] = [];
    for (const [key, value] of Object.entries(resp.headers)) {
      if (value !== undefined) {
        // Handle both string and string[] values
        if (Array.isArray(value)) {
          // For arrays, add each value as a separate header
          for (const val of value) {
            responseHeaders.push([key, val]);
          }
        } else {
          responseHeaders.push([key, value]);
        }
      }
    }

    const response = new Response(resp.body as any, {
      status: resp.statusCode,
      headers: Object.fromEntries(responseHeaders),
    });

    // Verbose logging for debugging - log response details
    if (shouldLog || process.env.VERBOSE_FETCH) {
      await logResponse(response);
    }

    if (!response.ok) {
      const requestId = response.headers.get("x-request-id");
      if (requestId) {
        console.debug(`Request ID: ${requestId}, Status: ${response.status}`);
      }
    }

    return response;
  } catch (error) {
    // Verbose logging for errors
    if (shouldLog || process.env.VERBOSE_FETCH) {
      logError(error);
    }
    if (error instanceof Error && error.name === "AbortError") {
      // Return a Response object that streamResponse etc can handle
      return new Response(null, {
        status: 499, // Client Closed Request
        statusText: "Client Closed Request",
      });
    }
    throw error;
  }
}

export default fetchWithRequestOptions;
