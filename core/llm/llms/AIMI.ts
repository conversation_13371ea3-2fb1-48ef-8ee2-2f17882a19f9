// 文件位置: core/llm/llms/AIMI.ts

import {
  ChatMessage,
  LLMFullCompletionOptions,
  LLMOptions,
} from "../../index.js";
import { LLMConfigurationStatuses } from "../constants.js";
import OpenAI from "./OpenAI.js";
import { LlmApiRequestType } from "../openaiTypeConverters";
import mergeJson from "../../util/merge";
import { fetchCompletionsWithRequestOptions } from "@continuedev/fetch";
import { renderChatMessage } from "../../util/messageContent";
import { setRequestId } from "../utils/requestIdHelper";
import { RuntimeContext } from "../../runtime/RuntimeContext";

/**
 * AIMI模型实现
 *
 * 基于OpenAI API格式的内部模型服务
 */
export class AIMI extends OpenAI {
  static providerName = "aimi";
  static defaultOptions: Partial<LLMOptions> = {
    apiBase: "https://idealab.alibaba-inc.com/api/openai/v1/",
    model: "gpt-4o-0806",
    apiKey: "8e25c3cf1b8f06af9f33bcb8eb4c06c2",
    contextLength: 8192, // GPT-4o的上下文长度
    maxEmbeddingBatchSize: 5,
    roles: ["chat", "edit", "apply"], // 默认角色
    capabilities: {
      tools: true,
      uploadImage: true,
    },
  };

  constructor(options: LLMOptions) {
    super(options);
    // 确保API基础URL以"/"结尾
    if (this.apiBase && !this.apiBase.endsWith("/")) {
      this.apiBase = `${this.apiBase}/`;
    }
  }

  protected useOpenAIAdapterFor: (LlmApiRequestType | "*")[] = [
    "chat",
    "embed",
    "list",
    "rerank",
    "streamChat",
    "streamComplete",
  ];

  /**
   * 检查配置状态
   */
  getConfigurationStatus() {
    if (!this.apiKey || !this.apiBase) {
      return LLMConfigurationStatuses.MISSING_API_KEY;
    }

    return LLMConfigurationStatuses.VALID;
  }

  supportsPrediction(): boolean {
    return true; // GPT-4o支持预测
  }

  /**
   * 检查是否支持流式补全
   */
  supportsFim(): boolean {
    return false; // GPT-4o支持流式补全
  }

  // 支持图像
  supportsImages(): boolean {
    return false; // GPT-4o支持图像
  }

  async *streamComplete2(
    _prompt: string,
    signal: AbortSignal,
    options: LLMFullCompletionOptions = {},
  ): AsyncGenerator<string, any> {
    const completionOptions = mergeJson(this.completionOptions, options);
    const logEnabled = options.log ?? false;
    const raw = options.raw ?? false;

    let prompt = _prompt;
    if (!raw) {
      if (this.templateMessages) {
        const msgs: ChatMessage[] = [{ role: "user", content: prompt }];
        prompt = this.templateMessages(msgs);
      }
    }

    const promptMessage = renderChatMessage({ role: "user", content: prompt });
    const args: any = this._convertArgs(completionOptions, []);
    args.prompt = promptMessage;
    args.messages = undefined;

    const response = await fetchCompletionsWithRequestOptions(
      this._getEndpoint("completions"),
      {
        method: "POST",
        headers: { ...this._getHeaders(), empId: RuntimeContext.empId },
        body: JSON.stringify({ ...args, ...this.extraBodyProperties() }),
        signal: signal,
      },
    );
    if (response.status === 499) {
      return; // Aborted by user
    }
    const jsonRsp: any = await response.json();
    const completion = jsonRsp?.choices?.[0]?.text;
    const requestId = jsonRsp?.extend_fields?.requestId ?? "";
    setRequestId(signal, requestId);
    if (completion) yield completion;
    return;
  }
}
