import { Tool } from "../..";
import { BUILT_IN_GROUP_NAME, BuiltInToolNames } from "../builtIn";
import { createSystemMessageExampleCall } from "../systemMessageTools/buildToolsSystemMessage";

export const globSearchTool: Tool = {
  type: "function",
  displayTitle: "搜索文件",
  wouldLikeTo: 'find file matches for "{{{ pattern }}}"',
  isCurrently: 'finding file matches for "{{{ pattern }}}"',
  hasAlready: 'retrieved file matches for "{{{ pattern }}}"',
  readonly: true,
  isInstant: true,
  group: BUILT_IN_GROUP_NAME,
  function: {
    name: BuiltInToolNames.FileGlobSearch,
    description: `
    Search for files in the project using glob patterns to find files by name or path.

    This tool is ideal for:
    - Finding files when you know part of the filename or path but don't know where it's located exactly
    - Locating files with specific extensions or patterns
    - Discovering files in unknown directory structures
    - Filtering files by naming conventions

    Glob pattern examples:
    - "*.js" - Find all JavaScript files
    - "**/*.test.ts" - Find all TypeScript test files
    - "src/**/Button*" - Find all files starting with "Button" in src directory
    - "components/*.tsx" - Find all TSX files in components directory

    Note: This tool only returns file paths, not file contents. Use readFile tool to get file contents.
  `.trim(),
    // "Search for files recursively in the project using glob patterns. Supports ** for recursive directory search. Output may be truncated; use targeted patterns",
    parameters: {
      type: "object",
      required: ["pattern"],
      properties: {
        pattern: {
          type: "string",
          description: "Glob pattern for file path matching",
        },
      },
    },
  },
  systemMessageDescription: createSystemMessageExampleCall(
    BuiltInToolNames.FileGlobSearch,
    `To return a list of files based on a glob search pattern, use the ${BuiltInToolNames.FileGlobSearch} tool`,
    [["pattern", "*.py"]],
  ),
};
