import { stat } from "fs/promises";

const TRUNCATED_MESSAGE =
  "To save on context only part of this file has been shown to you. The line range information is shown in the description above. You should retry this tool after you have searched inside the file with `grep -n` in order to find the line numbers of what you are looking for.";
const MAX_RESPONSE_LEN = 16000;
export const SNIPPET_LINES = 4;

export function maybeTruncate(
  content: string,
  truncateAfter: number = MAX_RESPONSE_LEN,
): { content: string; wasTruncated: boolean } {
  if (content.length <= truncateAfter) {
    return { content, wasTruncated: false };
  }

  // 找到截断点之前的最后一个完整行
  const truncatedPart = content.substring(0, truncateAfter);
  const lastNewlineIndex = truncatedPart.lastIndexOf("\n");

  // 如果找到了换行符，截断到最后一个完整行
  // 否则保留原始截断点（处理单行很长的情况）
  const truncatePoint = lastNewlineIndex > 0 ? lastNewlineIndex : truncateAfter;
  const truncatedContent = content.substring(0, truncatePoint);

  return {
    content: truncatedContent + "\n\n" + TRUNCATED_MESSAGE,
    wasTruncated: true,
  };
}

export function makeOutput(
  fileContent: string,
  fileDescriptor: string,
  initLine: number = 1,
): { content: string; description: string } {
  const truncateResult = maybeTruncate(fileContent);
  const truncatedLines = truncateResult.content.split("\n");

  // 如果被截断了，需要去掉截断消息行来计算实际内容行数
  let actualContentLines = truncatedLines.length;
  if (truncateResult.wasTruncated) {
    // 截断消息前有一个空行，所以减去2行（一个空行 + 截断消息）
    actualContentLines = truncatedLines.length - 2;
  }

  const endLine = initLine + actualContentLines - 1;

  // 生成描述信息
  let description: string;
  if (!truncateResult.wasTruncated && initLine === 1) {
    description = `Contents of ${fileDescriptor}`;
  } else if (truncateResult.wasTruncated) {
    description = `Lines ${initLine}-${endLine} of ${fileDescriptor} (truncated)`;
  } else {
    description = `Lines ${initLine}-${endLine} of ${fileDescriptor}`;
  }

  return {
    content: truncateResult.content,
    description: description,
  };
}

export async function isDirectory(path: string): Promise<boolean> {
  try {
    const stats = await stat(path);
    return stats.isDirectory();
  } catch {
    return false;
  }
}
