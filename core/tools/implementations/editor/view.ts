import type { ContextItem, ToolExtras } from "../../..";
import { isDirectory, makeOutput } from "./utils";
import { inferResolvedUriFromRelativePath } from "../../../util/ideUtils";

export async function handleView(
  resolvedFilepath: string | undefined,
  args: any,
  extras: ToolExtras,
): Promise<ContextItem[]> {
  const resolvedUri = await inferResolvedUriFromRelativePath(
    args.path,
    extras.ide,
  );
  resolvedFilepath = resolvedFilepath || resolvedUri;
  if (!resolvedFilepath) {
    return [
      {
        name: "View Failed",
        description: `Path ${resolvedFilepath} does not exist`,
        content: `Path ${args.path} does not exist. Please provide a valid path.`,
        type: "error",
      },
    ];
  }

  // Check if it's a directory
  if (await isDirectory(resolvedFilepath)) {
    // 目录处理逻辑
    try {
      const contents = await extras.ide.listDir(resolvedFilepath);
      // If listDir succeeds, it's a directory

      const fileList = contents
        .map(([path, type]) => `${path}${type === 2 ? "/" : ""}`)
        .join("\n");
      return [
        {
          name: "Directory Contents",
          description: `Contents of ${args.path}`,
          content: `Here's the files and directories up to 2 levels deep in ${args.path}, excluding hidden items:\n${fileList}\n`,
          type: "success",
          uri: {
            type: "file",
            value: resolvedFilepath,
          },
        },
      ];
    } catch (error) {
      // If listDir fails, assume it's a file and try to read it
    }
  }

  // Read file content
  const fileContent = await extras.ide.readFile(resolvedFilepath);
  const viewRange = args.view_range;

  if (viewRange && Array.isArray(viewRange) && viewRange.length === 2) {
    const [startLine, endLine] = viewRange;
    const lines = fileContent.split("\n");
    const totalLines = lines.length;

    if (startLine < 1 || startLine > totalLines) {
      return [
        {
          name: "View Failed",
          description: `Invalid view_range`,
          content: `Invalid view_range: ${viewRange}. Start line ${startLine} should be within the range [1, ${totalLines}]`,
          type: "error",
        },
      ];
    }

    const actualEndLine =
      endLine === -1 ? totalLines : Math.min(endLine, totalLines);
    if (actualEndLine < startLine) {
      return [
        {
          name: "View Failed",
          description: `Invalid view_range`,
          content: `Invalid view_range: ${viewRange}. End line ${actualEndLine} should be >= start line ${startLine}`,
          type: "error",
        },
      ];
    }

    const selectedLines = lines.slice(startLine - 1, actualEndLine);
    const selectedContent = selectedLines.join("\n");

    const output = makeOutput(selectedContent, args.path, startLine);

    return [
      {
        name: "File View",
        description: output.description,
        content: output.content,
        type: "success",
        uri: {
          type: "file",
          value: resolvedFilepath,
        },
      },
    ];
  }

  const output = makeOutput(fileContent, args.path);

  return [
    {
      name: "File View",
      description: output.description,
      content: output.content,
      type: "success",
      uri: {
        type: "file",
        value: resolvedFilepath,
      },
    },
  ];
}
