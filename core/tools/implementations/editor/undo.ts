import type { ContextItem, ToolExtras } from "../../..";
import { fileHistory, FileOperation } from "./types";
import { makeOutput } from "./utils";

export async function handleUndoEdit(
  resolvedFilepath: string | undefined,
  args: any,
  extras: ToolExtras,
): Promise<ContextItem[]> {
  if (!resolvedFilepath) {
    return [
      {
        name: "Undo Edit Failed",
        description: `File ${args.path} does not exist`,
        content: `File ${args.path} does not exist`,
        type: "error",
      },
    ];
  }

  const history = fileHistory.get(resolvedFilepath) || [];
  if (history.length === 0) {
    return [
      {
        name: "Undo Edit Failed",
        description: `No edit history found for ${args.path}`,
        content: `No edit history found for ${args.path}.`,
        type: "error",
      },
    ];
  }

  // Restore the last version from the last operation
  const lastOperation = history.pop()!;
  fileHistory.set(resolvedFilepath, history);

  if (lastOperation.command === "create") {
    // If the last operation was 'create', delete the file
    return await handleUndoCreate(extras, resolvedFilepath, args);
  } else {
    // For other operations (str_replace, insert), restore the previous content
    return await handleUndo(lastOperation, extras, resolvedFilepath, args);
  }
}

async function handleUndo(
  lastOperation: FileOperation,
  extras: ToolExtras,
  resolvedFilepath: string,
  args: any,
): Promise<ContextItem[]> {
  const previousContent = lastOperation.content;

  await extras.ide.writeFile(resolvedFilepath, previousContent);
  await extras.ide.saveFile(resolvedFilepath);

  // Refresh codebase index if available
  if (extras.codeBaseIndexer) {
    void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([resolvedFilepath]);
  }

  const fileOutput = makeOutput(previousContent, args.path);

  return [
    {
      name: "Undo Edit Completed",
      description: `Last edit to ${args.path} undone successfully. ${fileOutput.description}`,
      content: `Last edit to ${args.path} undone successfully. Here's the restored content:\n\n${fileOutput.content}`,
      type: "success",
      uri: {
        type: "file",
        value: resolvedFilepath,
      },
    },
  ];
}

async function handleUndoCreate(
  extras: ToolExtras,
  resolvedFilepath: string,
  args: any,
): Promise<ContextItem[]> {
  try {
    const deleteResult = await extras.ide.deleteFile(resolvedFilepath);

    if (!deleteResult) {
      return [
        {
          name: "Undo Edit Failed",
          description: `Failed to delete created file ${args.path}`,
          content: `Failed to delete created file ${args.path}. The file may be in use, or you may not have permission to delete it.`,
          type: "error",
        },
      ];
    }

    // Clear file history for the deleted file
    fileHistory.delete(resolvedFilepath);

    // Refresh codebase index if available
    if (extras.codeBaseIndexer) {
      void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([
        resolvedFilepath,
      ]);
    }

    return [
      {
        name: "Undo Edit Completed",
        description: `File creation undone, ${args.path} deleted successfully`,
        content: `File creation undone successfully. The file ${args.path} has been deleted.`,
        type: "success",
      },
    ];
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return [
      {
        name: "Undo Edit Failed",
        description: `Error deleting created file ${args.path}`,
        content: `Error deleting created file ${args.path}: ${errorMessage}`,
        type: "error",
      },
    ];
  }
}
