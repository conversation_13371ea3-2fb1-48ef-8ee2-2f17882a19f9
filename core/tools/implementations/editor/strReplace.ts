import type { ContextItem, ToolExtras } from "../../..";
import { FileOperation, fileHistory } from "./types";
import { makeOutput, SNIPPET_LINES } from "./utils";

export async function handleStrReplace(
  resolvedFilepath: string | undefined,
  args: any,
  extras: ToolExtras,
): Promise<ContextItem[]> {
  if (!resolvedFilepath) {
    return [
      {
        name: "String Replace Failed",
        description: `File ${args.path} does not exist`,
        content: `File ${args.path} does not exist`,
        type: "error",
      },
    ];
  }

  const oldStr = args.old_str;
  const newStr = args.new_str || "";

  if (oldStr === undefined) {
    return [
      {
        name: "String Replace Failed",
        description: "Parameter `old_str` is required for command: str_replace",
        content: "Parameter `old_str` is required for command: str_replace",
        type: "error",
      },
    ];
  }

  // Read current file content
  const fileContent = await extras.ide.readFile(resolvedFilepath);

  // Check if old_str exists and is unique
  const occurrences = (
    fileContent.match(
      new RegExp(oldStr.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "g"),
    ) || []
  ).length;

  if (occurrences === 0) {
    return [
      {
        name: "String Replace Failed",
        description: `No replacement was performed, old_str did not appear verbatim in ${args.path}`,
        content: `No replacement was performed, old_str \`${oldStr}\` did not appear verbatim in ${args.path}.`,
        type: "error",
      },
    ];
  }

  if (occurrences > 1) {
    return [
      {
        name: "String Replace Failed",
        description: `No replacement was performed, old_str appeared ${occurrences} times in ${args.path}`,
        content: `No replacement was performed, old_str \`${oldStr}\` appeared ${occurrences} times in ${args.path}. Please make sure to include enough context in \`old_str\` to make it unique.`,
        type: "error",
      },
    ];
  }

  // Find the line number where the replacement occurs for snippet generation
  const lines = fileContent.split("\n");
  let startLine = 1;
  for (let i = 0; i < lines.length; i++) {
    if (lines.slice(i).join("\n").startsWith(oldStr)) {
      startLine = i + 1;
      break;
    }
  }

  // Save current operation to history before making changes
  const history = fileHistory.get(resolvedFilepath) || [];
  const strReplaceOperation: FileOperation = {
    command: "str_replace",
    path: resolvedFilepath,
    content: fileContent,
    fileDescriptor: args.path,
  };
  history.push(strReplaceOperation);
  fileHistory.set(resolvedFilepath, history);

  // Perform replacement
  const newFileContent = fileContent.replace(oldStr, newStr);

  await extras.ide.writeFile(resolvedFilepath, newFileContent);
  await extras.ide.saveFile(resolvedFilepath);

  // Refresh codebase index if available
  if (extras.codeBaseIndexer) {
    void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([resolvedFilepath]);
  }

  // Create snippet showing the replacement
  const newLines = newFileContent.split("\n");
  const newStrLines = newStr.split("\n");
  const endLine = Math.min(startLine + SNIPPET_LINES + newStrLines.length - 1, newLines.length);

  const snippetLines = newLines.slice(startLine - 1, endLine);
  const snippet = snippetLines.join("\n");

  const snippetOutput = makeOutput(snippet, `a snippet of ${args.path}`, startLine);

  return [
    {
      name: "String Replace Completed",
      description: `The file ${args.path} has been edited. ${snippetOutput.description}`,
      content: `The file ${args.path} has been edited. Here's the affected section:\n\n${snippetOutput.content}\n\nReview the changes and make sure they are as expected. Edit the file again if necessary.`,
      type: "success",
      uri: {
        type: "file",
        value: resolvedFilepath,
      },
    },
  ];
}
