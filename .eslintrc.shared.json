{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 6, "sourceType": "module"}, "plugins": ["@typescript-eslint", "import"], "rules": {"no-negated-condition": "warn", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-floating-promises": "warn", "@typescript-eslint/semi": "warn", "@typescript-eslint/no-misused-promises": "error", "curly": "warn", "eqeqeq": "warn", "no-throw-literal": "warn", "semi": "off", "import/order": ["warn", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index", "object", "type"], "alphabetize": {"order": "asc", "caseInsensitive": true}, "newlines-between": "always"}]}, "ignorePatterns": ["out", "dist", "**/*.d.ts"]}